<?php $__env->startSection('title', 'Inventory Card'); ?>
<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/site/inventorycard.js']); ?>
<?php $__env->stopSection(); ?>
<?php
    $currentSiteId = session('site_id');
?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Inventory Card</li>
                    </ol>
                </div>
                <h4 class="page-title">Inventory Card</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="search-input" class="form-control" placeholder="Cari Part...">
                                <button class="btn btn-primary" type="button" id="search-button">
                                    <i class="mdi mdi-magnify"></i> Cari
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <select id="part-type-filter" class="btn btn-primary ml-2 mr-2">
                                    <option value="">Semua Tipe</option>
                                    <?php $__currentLoopData = \App\Models\Part::PART_TYPES; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type); ?>"><?php echo e($type); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <button class="btn btn-primary" type="button" id="filter-button">
                                    <i class="mdi mdi-filter"></i> Filter
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden input to store site ID -->
                    <input type="hidden" id="current-site-id" value="<?php echo e($currentSiteId); ?>">

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover w-100">
                            <thead class="table-dark text-white">
                                <tr>
                                    <th class="p-2">Part Code</th>
                                    <?php if($currentSiteId === 'IMK'): ?>
                                    <th class="p-2">Item Code</th>
                                    <?php endif; ?>
                                    <th class="p-2">Nama Part</th>
                                    <th class="p-2">Stock Saat Ini</th>
                                    <th class="p-2">Min Stock</th>
                                    <th class="p-2">Max Stock</th>
                                    <th class="p-2">Harga</th>
                                    <th class="p-2">Status</th>
                                </tr>
                            </thead>
                            <tbody id="inventory-table-body">
                                <!-- Data will be loaded here via AJAX -->
                                <tr>
                                    <td colspan="<?php echo e($currentSiteId === 'IMK' ? 8 : 7); ?>" class="text-center">Loading data...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <div id="pagination-info" class="text-center mb-2 text-muted small">
                            <!-- Page info will be displayed here -->
                        </div>
                        <div id="inventory-pagination" class="d-flex justify-content-center">
                            <!-- Custom pagination will be rendered here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sites/inventorycard.blade.php ENDPATH**/ ?>