[2025-05-27 13:13:05] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-05-24  
[2025-05-27 13:13:14] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":815,"quantity":1,"price":"5200000.00","status":"Not Ready","is_custom":false,"part_code":"C-SD-8126-PWB","part_name":"COMPRESSOR SD7H15 S8126 DOUBLE PULLEY A"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 0 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":828,"quantity":1,"price":"2650000.00","status":"Not Ready","is_custom":false,"part_code":"CD-142320-PWB","part_name":"CONDENSOR R134 14 x 23 x 20MM"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":1040,"quantity":2,"price":"3850000.00","status":"Not Ready","is_custom":false,"part_code":"MFC-TKD-24-PWB","part_name":"MOTOR FAN CONDENSOR TKD"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 2 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 3 {"part_data":{"part_inventory_id":1073,"quantity":1,"price":"400000.00","status":"Not Ready","is_custom":false,"part_code":"RD-C19-PWB","part_name":"RECEIVER DRYER R12 NUT 19"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 3 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 4 {"part_data":{"part_inventory_id":930,"quantity":1,"price":"600000.00","status":"Not Ready","is_custom":false,"part_code":"EV-R12-PC200-PWB","part_name":"EXPANTION VALVE KUNINGAN R12"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 4 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 5 {"part_data":{"part_inventory_id":100002,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5004","part_name":"Hose 5/8 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 5 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 6 {"part_data":{"part_inventory_id":100003,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5446","part_name":"Hose 3/8 Assy 1","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 6 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 7 {"part_data":{"part_inventory_id":100004,"quantity":1,"price":"600000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-4552","part_name":"Hose 3/8 Assy 2","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 7 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 8 {"part_data":{"part_inventory_id":100005,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-3869","part_name":"Hose 1/2 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 8 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 9 {"part_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 9 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 10 {"part_data":{"part_inventory_id":100007,"quantity":1,"price":"500000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5055","part_name":"Labour Cost","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 10 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 11 {"part_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 11 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 12 {"part_data":{"part_inventory_id":100026,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":false,"part_code":".erewr","part_name":".lllll"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 12 is regular, validating part_inventory_id  
[2025-05-27 13:13:14] local.INFO: Validating part at index 13 {"part_data":{"part_inventory_id":100027,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164s","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"AC","purchase_price":700000,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 13 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Validating part at index 14 {"part_data":{"part_inventory_id":100028,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-9723","part_name":"Vacuuaaaaa","part_type":"PERSEDIAAN LAINNYA","purchase_price":2000000,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:14] local.INFO: Part 14 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100002,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5004","part_name":"Hose 5/8 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-5004"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-5004","part_inventory_id":100002} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100003,"quantity":1,"price":"900000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5446","part_name":"Hose 3/8 Assy 1","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-5446"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-5446","part_inventory_id":100003} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100004,"quantity":1,"price":"600000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-4552","part_name":"Hose 3/8 Assy 2","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-4552"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-4552","part_inventory_id":100004} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100005,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-3869","part_name":"Hose 1/2 Assy","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-3869"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-3869","part_inventory_id":100005} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-1164"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-1164","part_inventory_id":100006} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100007,"quantity":1,"price":"500000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-5055","part_name":"Labour Cost","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-5055"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-5055","part_inventory_id":100007} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100006,"quantity":1,"price":"850000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-1164"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-1164","part_inventory_id":100006} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100027,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250524-1164s","part_name":"Vacuum + Oil Compressor + Freon R134a","part_type":"AC","purchase_price":700000,"eum":"EA"},"purchase_price_raw":700000,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"AC","purchase_price":700000,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250524-1164s"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250524-1164s","part_inventory_id":100027} 
[2025-05-27 13:13:14] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100028,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-9723","part_name":"Vacuuaaaaa","part_type":"PERSEDIAAN LAINNYA","purchase_price":2000000,"eum":"EA"},"purchase_price_raw":2000000,"purchase_price_type":"integer"} 
[2025-05-27 13:13:14] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":2000000,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:14] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250526-9723"} 
[2025-05-27 13:13:14] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250526-9723","part_inventory_id":100028} 
[2025-05-27 13:13:20] local.INFO: getInvoiceData called for penawaran ID: 5  
[2025-05-27 13:13:20] local.INFO: Penawaran does not have invoice  
[2025-05-27 13:13:20] local.INFO: getInvoiceData called for penawaran ID: 5  
[2025-05-27 13:13:20] local.INFO: Penawaran does not have invoice  
[2025-05-27 13:13:24] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-05-18  
[2025-05-27 13:13:33] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":2356,"quantity":1,"price":"21621622.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250519-2928","part_name":"Install New AC","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:33] local.INFO: Part 0 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:33] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":799,"quantity":1,"price":"0.00","status":"Not Ready","is_custom":false,"part_code":"C-10S-13C-24-B-ASSY-PWB","part_name":"COMPRESSOR ND 10S 13C 24V SINGLE PULLEY B ASSY"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-05-27 13:13:33] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-05-27 13:13:33] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":100029,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-3099","part_name":"avavava","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"is_custom":true,"is_custom_type":"boolean"} 
[2025-05-27 13:13:33] local.INFO: Part 2 is custom, validating part_code, part_name, and additional fields  
[2025-05-27 13:13:33] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":2356,"quantity":1,"price":"21621622.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250519-2928","part_name":"Install New AC","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:33] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:33] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250519-2928"} 
[2025-05-27 13:13:33] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250519-2928","part_inventory_id":2356} 
[2025-05-27 13:13:33] local.INFO: Creating custom part with WHO inventory {"incoming_data":{"part_inventory_id":100029,"quantity":1,"price":"2000000.00","status":"Not Ready","is_custom":true,"part_code":"CUSTOM-20250526-3099","part_name":"avavava","part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA"},"purchase_price_raw":0,"purchase_price_type":"integer"} 
[2025-05-27 13:13:33] local.INFO: Extracted part data values {"part_type":"PERSEDIAAN LAINNYA","purchase_price":0,"eum":"EA","bin_location":"-"} 
[2025-05-27 13:13:33] local.INFO: Part already exists in main parts table {"part_code":"CUSTOM-20250526-3099"} 
[2025-05-27 13:13:33] local.INFO: WHO inventory record already exists for part {"part_code":"CUSTOM-20250526-3099","part_inventory_id":100029} 
[2025-05-27 13:13:38] local.INFO: getInvoiceData called for penawaran ID: 4  
[2025-05-27 13:13:38] local.INFO: Penawaran has invoice:  {"invoice_id":20} 
[2025-05-27 13:13:39] local.INFO: getInvoiceData called for penawaran ID: 4  
[2025-05-27 13:13:39] local.INFO: Penawaran has invoice:  {"invoice_id":20} 
[2025-05-27 13:25:34] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:25:36] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-27  
[2025-05-27 13:25:39] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:26:07] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-27  
[2025-05-27 13:26:11] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-27  
[2025-05-27 13:26:13] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:26:52] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:27:05] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:30:11] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:30:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-27  
[2025-05-27 13:30:31] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:30:48] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:31:25] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:31:40] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:32:02] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:32:52] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:33:04] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:34:37] local.INFO: Getting invoice details for ID: manual  
[2025-05-27 13:34:37] local.ERROR: Error getting invoice details: No query results for model [App\Models\Invoice] manual  
[2025-05-27 13:34:37] local.ERROR: Invoice ID: manual  
[2025-05-27 13:34:37] local.ERROR: #0 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->findOrFail('manual')
#1 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2372): Illuminate\Database\Eloquent\Model->forwardCallTo(Object(Illuminate\Database\Eloquent\Builder), 'findOrFail', Array)
#2 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2384): Illuminate\Database\Eloquent\Model->__call('findOrFail', Array)
#3 C:\xampp\htdocs\portalpwb\app\Http\Controllers\InvoiceController.php(138): Illuminate\Database\Eloquent\Model::__callStatic('findOrFail', Array)
#4 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(47): App\Http\Controllers\InvoiceController->getInvoiceDetails('manual')
#5 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(266): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\InvoiceController), 'getInvoiceDetai...')
#6 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\Route->runController()
#7 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#8 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#9 C:\xampp\htdocs\portalpwb\app\Http\Middleware\CheckSales.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\CheckSales->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\portalpwb\app\Http\Middleware\EnsureLogDescriptions.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\EnsureLogDescriptions->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(51): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#14 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(88): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#17 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#21 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(75): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#28 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#29 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#30 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#31 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(201): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#32 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#33 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#35 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(110): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#51 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(145): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#52 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1220): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#53 C:\xampp\htdocs\portalpwb\public\index.php(17): Illuminate\Foundation\Application->handleRequest(Object(Illuminate\Http\Request))
#54 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(23): require_once('C:\\xampp\\htdocs...')
#55 {main}  
[2025-05-27 13:34:40] local.INFO: Getting invoice details for ID: manual  
[2025-05-27 13:34:40] local.ERROR: Error getting invoice details: No query results for model [App\Models\Invoice] manual  
[2025-05-27 13:34:40] local.ERROR: Invoice ID: manual  
[2025-05-27 13:34:40] local.ERROR: #0 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->findOrFail('manual')
#1 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2372): Illuminate\Database\Eloquent\Model->forwardCallTo(Object(Illuminate\Database\Eloquent\Builder), 'findOrFail', Array)
#2 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2384): Illuminate\Database\Eloquent\Model->__call('findOrFail', Array)
#3 C:\xampp\htdocs\portalpwb\app\Http\Controllers\InvoiceController.php(138): Illuminate\Database\Eloquent\Model::__callStatic('findOrFail', Array)
#4 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(47): App\Http\Controllers\InvoiceController->getInvoiceDetails('manual')
#5 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(266): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\InvoiceController), 'getInvoiceDetai...')
#6 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\Route->runController()
#7 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#8 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#9 C:\xampp\htdocs\portalpwb\app\Http\Middleware\CheckSales.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\CheckSales->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\portalpwb\app\Http\Middleware\EnsureLogDescriptions.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\EnsureLogDescriptions->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(51): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#14 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(88): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#17 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#19 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#20 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#21 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(75): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#28 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#29 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#30 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#31 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(201): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#32 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#33 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#35 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(110): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#51 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(145): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#52 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1220): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#53 C:\xampp\htdocs\portalpwb\public\index.php(17): Illuminate\Foundation\Application->handleRequest(Object(Illuminate\Http\Request))
#54 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(23): require_once('C:\\xampp\\htdocs...')
#55 {main}  
[2025-05-27 13:34:51] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:35:02] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-05-21  
[2025-05-27 13:39:33] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-05-27 13:39:39] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-05-27 14:03:06] local.ERROR: Attempt to read property "part_code" on null {"userId":"2025005","exception":"[object] (ErrorException(code: 0): Attempt to read property \"part_code\" on null at C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\InventoryCardController.php:93)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 93)
#1 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\InventoryCardController.php(93): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 93)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Sites\\InventoryCardController->getInventoryData(Object(Illuminate\\Http\\Request))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Sites\\InventoryCardController), 'getInventoryDat...')
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\EnsureLogDescriptions.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureLogDescriptions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#51 {main}
"} 
[2025-05-27 14:03:13] local.ERROR: Attempt to read property "part_code" on null {"userId":"2025005","exception":"[object] (ErrorException(code: 0): Attempt to read property \"part_code\" on null at C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\InventoryCardController.php:93)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 93)
#1 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\InventoryCardController.php(93): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 93)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Sites\\InventoryCardController->getInventoryData(Object(Illuminate\\Http\\Request))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Sites\\InventoryCardController), 'getInventoryDat...')
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\EnsureLogDescriptions.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureLogDescriptions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#51 {main}
"} 
[2025-05-27 14:03:18] local.ERROR: Attempt to read property "part_code" on null {"userId":"2025005","exception":"[object] (ErrorException(code: 0): Attempt to read property \"part_code\" on null at C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\InventoryCardController.php:93)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 93)
#1 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\InventoryCardController.php(93): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 93)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Sites\\InventoryCardController->getInventoryData(Object(Illuminate\\Http\\Request))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Sites\\InventoryCardController), 'getInventoryDat...')
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\EnsureLogDescriptions.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\EnsureLogDescriptions->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#51 {main}
"} 
