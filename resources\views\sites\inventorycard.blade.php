@extends('sites.content')
@section('title', 'Inventory Card')
@section('resourcesite')
@vite(['resources/js/site/inventorycard.js'])
@endsection
@php
    $currentSiteId = session('site_id');
@endphp

@section('contentsite')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('sites.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Inventory Card</li>
                    </ol>
                </div>
                <h4 class="page-title">Inventory Card</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="search-input" class="form-control" placeholder="Cari Part...">
                                <button class="btn btn-primary" type="button" id="search-button">
                                    <i class="mdi mdi-magnify"></i> Cari
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <select id="part-type-filter" class="btn btn-primary ml-2 mr-2">
                                    <option value="">Semua Tipe</option>
                                    @foreach(\App\Models\Part::PART_TYPES as $type)
                                        <option value="{{ $type }}">{{ $type }}</option>
                                    @endforeach
                                </select>
                                <button class="btn btn-primary" type="button" id="filter-button">
                                    <i class="mdi mdi-filter"></i> Filter
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden input to store site ID -->
                    <input type="hidden" id="current-site-id" value="{{ $currentSiteId }}">

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover w-100">
                            <thead class="table-dark text-white">
                                <tr>
                                    <th class="p-2">Part Code</th>
                                    @if($currentSiteId === 'IMK')
                                    <th class="p-2">Item Code</th>
                                    @endif
                                    <th class="p-2">Nama Part</th>
                                    <th class="p-2">Stock Saat Ini</th>
                                    <th class="p-2">Min Stock</th>
                                    <th class="p-2">Max Stock</th>
                                    <th class="p-2">Harga</th>
                                    <th class="p-2">Status</th>
                                </tr>
                            </thead>
                            <tbody id="inventory-table-body">
                                <!-- Data will be loaded here via AJAX -->
                                <tr>
                                    <td colspan="{{ $currentSiteId === 'IMK' ? 8 : 7 }}" class="text-center">Loading data...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <div id="pagination-info" class="text-center mb-2 text-muted small">
                            <!-- Page info will be displayed here -->
                        </div>
                        <div id="inventory-pagination" class="d-flex justify-content-center">
                            <!-- Custom pagination will be rendered here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
